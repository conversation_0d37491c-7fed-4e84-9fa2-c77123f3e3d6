import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import ChatBubbleIcon from '../Icons/ChatBubbleIcon';

const DResponsiveTable = ({
  columns,
  data,
  className = '',
  containerClassName = '',
  minWidth = 'min-w-[600px] sm:min-w-[700px] lg:min-w-full',
  maxHeight = 'max-h-[calc(100vh-280px)]',
  minHeight = 'min-h-[120px]',
  renderCell,
  emptyState,
  ...props
}) => {
  const isAboveSm = useIsAboveBreakpoint('sm');
  const isAboveMd = useIsAboveBreakpoint('md');

  const defaultEmptyState = (
    <div className="flex flex-col items-center justify-center w-full h-full py-size5 sm:py-size7 px-size3 sm:px-size5">
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-size3 bg-grey-5 rounded-full flex items-center justify-center">
          <ChatBubbleIcon />
        </div>
        <p className="text-base sm:text-lg text-grey-50 text-center mb-size1">
          No data found
        </p>
      </div>
    </div>
  );

  if (!data || data.length === 0) {
    return emptyState || defaultEmptyState;
  }

  return (
    <div className={`overflow-x-auto rounded-size1 border border-grey-5 bg-white shadow-sm ${maxHeight} ${minHeight} ${
      isAboveSm ? 'overflow-y-auto no-scrollbar' : 'overflow-y-auto scrollbar'
    } ${containerClassName}`}>
      <div className={minWidth}>
        <table className={`w-full table-auto text-left ${className}`} {...props}>
          <thead className="bg-grey-5 sticky top-0 z-10">
            <tr>
              {columns.map((column, index) => (
                <th
                  key={column.key || index}
                  className={`px-size1 py-size2 text-sm font-medium text-grey-50 tracking-tight ${
                    column.minWidth || (isAboveMd ? 'min-w-24' : 'min-w-16')
                  } ${column.headerClassName || ''}`}
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((item, rowIndex) => (
              <tr key={rowIndex} className="h-14 border-b border-grey-5 last:border-b-0">
                {columns.map((column, colIndex) => (
                  <td
                    key={column.key || colIndex}
                    className={`px-size1 py-size2 ${column.cellClassName || ''}`}
                  >
                    {renderCell ? (
                      renderCell(item, column, rowIndex, colIndex)
                    ) : (
                      <div className="flex items-center gap-size1">
                        <span className={`${isAboveMd ? 'text-base' : 'text-sm'} font-regular tracking-tight`}>
                          {item[column.key]}
                        </span>
                      </div>
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DResponsiveTable;
