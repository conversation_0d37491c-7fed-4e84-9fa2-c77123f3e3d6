import DButton from '@/components/Global/DButton';
import DResponsiveTable from '@/components/Global/DResponsiveTable';
import useDanteApi from '@/hooks/useDanteApi';
import { getVoiceConversations } from '@/services/voice.service';
import { useEffect, useState, useRef } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { DateTime } from 'luxon';
import DBadge from '@/components/Global/DBadge';
import { STATUS } from '@/constants';
import DSwitch from '@/components/Global/DSwitch';
import useLayoutStore from '@/stores/layout/layoutStore';
import DLoading from '@/components/DLoading';
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';

const VoiceConversations = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const params = useParams();
    const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
    const switchRef = useRef(null);

    // Responsive breakpoints
    const isAboveSm = useIsAboveBreakpoint('sm');
    const isAboveMd = useIsAboveBreakpoint('md');

    // Check if we're coming from the onboarding popup
    const searchParams = new URLSearchParams(location.search);
    const fromOnboarding = searchParams.get('source') === 'onboarding-popup';

    const { data: conversations, isLoading, refetch } = useDanteApi(getVoiceConversations, [], { }, params.id);

    const [conversationsData, setConversationsData] = useState([]);
    const [liveConversations, setLiveConversations] = useState(fromOnboarding);

    // Table columns configuration
    const tableColumns = [
        {
            key: 'caller_number',
            label: 'Caller Number',
            minWidth: isAboveMd ? 'min-w-32' : 'min-w-20',
        },
        {
            key: 'date_started',
            label: 'Date started',
            minWidth: isAboveMd ? 'min-w-36' : 'min-w-24',
        },
        {
            key: 'date_ended',
            label: 'Date ended',
            minWidth: isAboveMd ? 'min-w-36' : 'min-w-24',
        },
        {
            key: 'status',
            label: 'Status',
            minWidth: isAboveMd ? 'min-w-24' : 'min-w-16',
        },
        {
            key: 'duration',
            label: 'Duration',
            minWidth: isAboveMd ? 'min-w-20' : 'min-w-16',
        },
        {
            key: 'actions',
            label: 'Actions',
            minWidth: isAboveMd ? 'min-w-24' : 'min-w-16',
        }
    ];

    
    const renderCell = (item, column, rowIndex, colIndex) => {
        switch (column.key) {
            case 'caller_number':
                return (
                    <div className="flex items-center gap-size1">
                        <span className={`${isAboveMd ? 'text-base' : 'text-sm'} font-regular tracking-tight`}>
                            {item.caller_number}
                        </span>
                    </div>
                );
            case 'date_started':
                return (
                    <span className={`${isAboveMd ? 'text-base' : 'text-sm'} font-regular tracking-tight`}>
                        {item.call_started_at ? DateTime.fromISO(item.call_started_at).toLocaleString(DateTime.DATETIME_SHORT) : ''}
                    </span>
                );
            case 'date_ended':
                return (
                    <span className={`${isAboveMd ? 'text-base' : 'text-sm'} font-regular tracking-tight`}>
                        {item.call_ended_at ? DateTime.fromISO(item.call_ended_at).toLocaleString(DateTime.DATETIME_SHORT) : ''}
                    </span>
                );
            case 'status':
                return (
                    <DBadge
                        label={item.status.replace(/_/g, ' ').toUpperCase()}
                        type={item.status === 'done' ? STATUS.SUCCESS : STATUS.WORKING}
                        showIcon={false}
                    />
                );
            case 'duration':
                return (
                    <span className={`${isAboveMd ? 'text-base' : 'text-sm'} font-regular tracking-tight`}>
                        {item?.duration ? `${Math.floor(item.duration / 60)}m ${item.duration % 60}s` : '0m 0s'}
                    </span>
                );
            case 'actions':
                return (
                    <DButton
                        variant="outlined"
                        size="sm"
                        onClick={() => navigate(`${item.id}`)}
                    >
                        View
                    </DButton>
                );
            default:
                return (
                    <span className={`${isAboveMd ? 'text-base' : 'text-sm'} font-regular tracking-tight`}>
                        {item[column.key]}
                    </span>
                );
        }
    };

    
    const emptyState = (
        <div className="flex flex-col items-center justify-center w-full h-full py-size5 sm:py-size7 px-size3 sm:px-size5">
            <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-size3 bg-grey-5 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-grey-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                </div>
                <p className="text-base sm:text-lg text-grey-50 text-center mb-size1">
                    No conversations found
                </p>
            </div>
        </div>
    );

    useEffect(() => {
        if (conversations?.results?.length > 0) {
            setConversationsData(conversations.results);
        } else {
            setConversationsData([]);
        }
    }, [conversations]);

    useEffect(() => {
        if (liveConversations) {
            const interval = setInterval(() => {
                refetch();
            }, 3000);
            return () => clearInterval(interval);
        }
    }, [liveConversations]);

    useEffect(() => {
        setSidebarOpen(false);
    }, []);

    // Add glow effect if coming from onboarding
    useEffect(() => {
        if (fromOnboarding && switchRef.current) {
            // Clear the source parameter from the URL
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete('source');
            window.history.replaceState({}, '', newUrl.toString());
        }
    }, [fromOnboarding]);

    // if(isLoading) {
    //     return <DLoading show={true} />
    // }

    return (
        <div className={`flex flex-col bg-white rounded-size1 h-[1px] grow overflow-y-auto  ${
            isAboveSm ? 'gap-size5 p-size5 no-scrollbar' : 'gap-size3 p-size3 scrollbar'
        } ${isAboveMd ? 'p-size6' : ''}`}>
            <div className="flex items-center justify-between w-full">
              
                {isAboveMd && (
                    <h1 className="text-xl font-medium text-grey-400">AI Voice Conversations</h1>
                )}

               
                <div
                    ref={switchRef}
                    className={`${isAboveMd ? 'self-end' : 'ml-auto'} ${
                        fromOnboarding
                            ? `animate-pulse shadow-md shadow-purple-20 rounded-lg ${isAboveSm ? 'p-2' : 'p-1'}`
                            : ''
                    }`}
                >
                    <DSwitch
                        label="Enable live conversations"
                        checked={liveConversations}
                        onChange={(checked) => setLiveConversations(checked)}
                    />
                </div>
            </div>
            <div className="flex flex-col gap-size1 sm:gap-size2 w-full h-full">
                {isLoading ? (
                    <div className="flex items-center justify-center w-full h-full py-size7">
                        <DLoading show={true} />
                    </div>
                ) : (
                    <DResponsiveTable
                        columns={tableColumns}
                        data={conversationsData}
                        renderCell={renderCell}
                        emptyState={emptyState}
                        className={`${isAboveMd ? 'text-base' : 'text-sm'}`}
                    />
                )}
            </div>
        </div>
    )
}

export default VoiceConversations;